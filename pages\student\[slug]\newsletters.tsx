// pages/student/[slug]/newsletters.tsx
import { useParams } from 'blade/hooks';
import { use } from 'blade/server/hooks';
import StudentNewslettersPage from '../../../components/student/StudentNewslettersPage.client';

const StudentNewslettersPageRoute = () => {
  const { slug } = useParams();

  // Server-side data fetching - get current student
  const student = use.users({
    with: { slug: slug, role: 'student' }
  });

  if (!student) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-red-600">Student not found</h1>
      </div>
    );
  }

  return <StudentNewslettersPage student={student} />;
};

export default StudentNewslettersPageRoute;
