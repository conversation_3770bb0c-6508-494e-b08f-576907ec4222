// schema/index.ts
import { blob, boolean, date, link, model, string } from 'ronin/schema';

// Extended User model with role management and Better Auth fields
export const User = model({
  slug: 'user',
  fields: {
    email: string({ required: true, unique: true }),
    emailVerified: boolean({ required: true, defaultValue: false }),
    image: blob(),
    name: string({ required: true }),
    // Role field for Better Auth admin plugin and our role system
    role: string({ required: true }), // 'student' | 'teacher' | 'school_admin'
    slug: string({ required: true }), // User slug for URLs
    // Better Auth username plugin fields
    username: string({ unique: true }), // Required by username plugin
    displayUsername: string(), // Non-normalized username
    // Student fields
    teacherId: string(),
    isActive: boolean(),
    classId: string(),
    grade: string(),
    // Teacher fields
    isIndependent: boolean(),
    schoolId: string(),
    department: string(),
    subjects: string(), // JSON string of array
    isVerified: boolean(),
    // School admin fields
    schoolName: string(),
    schoolAddress: string(),
    schoolPlaceId: string(),
    schoolType: string(),
    schoolDistrict: string(),
    studentCount: string(), // Store as string to avoid number issues
    teacherCount: string(), // Store as string to avoid number issues
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// Better Auth required models
export const Session = model({
  slug: 'session',
  fields: {
    expiresAt: date({ required: true }),
    ipAddress: string(),
    token: string({ required: true, unique: true }),
    userId: link({ required: true, target: 'user' }),
    userAgent: string(),
    activeOrganizationId: string(), // For organization plugin
  },
});

export const Account = model({
  slug: 'account',
  pluralSlug: 'accounts',
  fields: {
    accessToken: string(),
    accessTokenExpiresAt: date(),
    accountId: string({ required: true }),
    idToken: string(),
    password: string(),
    providerId: string({ required: true }),
    refreshToken: string(),
    refreshTokenExpiresAt: date(),
    scope: string(),
    userId: link({ required: true, target: 'user' }),
  },
});

export const Verification = model({
  slug: 'verification',
  pluralSlug: 'verifications',
  fields: {
    expiresAt: date({ required: true }),
    identifier: string({ required: true }),
    value: string({ required: true }),
  },
});

// OTP model for emailOTP plugin
export const Otp = model({
  slug: 'otp',
  pluralSlug: 'otps',
  fields: {
    identifier: string({ required: true }), // email address
    value: string({ required: true }), // the OTP code
    expiresAt: date({ required: true }),
    type: string({ required: true }), // 'sign-in', 'email-verification', 'forget-password'
    attempts: string({ required: true }), // number of attempts (stored as string)
    createdAt: date({ required: true }),
  },
});

// Organization plugin models - CORRECTED
export const Organization = model({
  slug: 'organization',
  fields: {
    name: string({ required: true }),
    slug: string({ required: true, unique: true }),
    logo: string(),
    metadata: string(), // JSON string for additional data
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// Fix the Member model - this is what Better Auth expects
export const Member = model({
  slug: 'member', // This must be 'member' (singular)
  fields: {
    userId: link({ required: true, target: 'user' }),
    organizationId: link({ required: true, target: 'organization' }),
    role: string({ required: true }), // 'owner', 'admin', 'member'
    createdAt: date({ required: true }),
  },
});

export const Invitation = model({
  slug: 'invitation',
  fields: {
    organizationId: link({ required: true, target: 'organization' }),
    email: string({ required: true }),
    role: string({ required: true }),
    status: string({ required: true }), // 'pending', 'accepted', 'declined', 'expired'
    expiresAt: date({ required: true }),
    inviterId: link({ required: true, target: 'user' }),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

// Legacy models - you can keep these for backward compatibility
export const School = model({
  slug: 'school',
  fields: {
    name: string({ required: true }),
    domain: string({ unique: true }), // Optional: for domain-based signup
    adminUserId: link({ required: true, target: 'user' }),
    address: string(),
    phone: string(),
    isActive: boolean({ required: true }),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

export const Teacher = model({
  slug: 'teacher',
  fields: {
    userId: link({ required: true, target: 'user' }),
    schoolId: link({ target: 'school' }), // Optional: independent teachers
    subjects: string(), // JSON array of subjects
    gradeLevel: string(),
    bio: string(),
    isActive: boolean({ required: true }),
    createdAt: date({ required: true }),
    updatedAt: date({ required: true }),
  },
});

export const SchoolTeacher = model({
  slug: 'school_teacher',
  fields: {
    schoolId: link({ required: true, target: 'school' }),
    teacherId: link({ required: true, target: 'teacher' }),
    invitedBy: link({ required: true, target: 'user' }),
    status: string({ required: true }), // 'pending' | 'accepted' | 'declined'
    invitedAt: date({ required: true }),
    respondedAt: date(),
  },
});

// Waitlist model for signup tracking
export const Waitlist = model({
  slug: 'waitlist',
  fields: {
    email: string({ required: true, unique: true }),
    userType: string({ required: true }), // 'teacher' | 'school_admin'
    name: string({ required: true }), // Full name is now required
    // School-specific fields
    schoolName: string(),
    schoolAddress: string(),
    schoolPlaceId: string(),
    schoolType: string(),
    schoolDistrict: string(),
    studentCount: string(),
    teacherCount: string(),
    createdAt: date({ required: true }),
    isApproved: boolean({ required: true, defaultValue: false }),
  },
});