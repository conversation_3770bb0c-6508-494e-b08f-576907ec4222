// components/student/StudentRemindersPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';
import { Bell, Calendar, Clock, AlertTriangle, BookOpen } from 'lucide-react';
import { cn } from '../../lib/utils';

interface Student {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
}

interface StudentRemindersPageProps {
  student: Student;
}

// Mock data for reminders - in a real app, this would come from the server
const mockReminders = [
  {
    id: '1',
    title: 'Math Test Tomorrow',
    message: 'Don\'t forget to study Chapter 5-7 for the algebra test',
    type: 'test',
    priority: 'high',
    dueDate: '2024-01-25T09:00:00',
    subject: 'Mathematics'
  },
  {
    id: '2',
    title: 'Science Project Due',
    message: 'Submit your photosynthesis lab report by end of day',
    type: 'assignment',
    priority: 'medium',
    dueDate: '2024-01-27T23:59:00',
    subject: 'Science'
  },
  {
    id: '3',
    title: 'Parent-Teacher Conference',
    message: 'Meeting scheduled with Mrs. <PERSON> at 3:00 PM',
    type: 'meeting',
    priority: 'medium',
    dueDate: '2024-01-26T15:00:00',
    subject: 'General'
  },
  {
    id: '4',
    title: 'Library Book Return',
    message: 'Return "To Kill a Mockingbird" to avoid late fees',
    type: 'library',
    priority: 'low',
    dueDate: '2024-01-28T17:00:00',
    subject: 'Library'
  },
  {
    id: '5',
    title: 'Field Trip Permission Slip',
    message: 'Get permission slip signed for next week\'s museum visit',
    type: 'permission',
    priority: 'medium',
    dueDate: '2024-01-29T08:00:00',
    subject: 'History'
  }
];

const StudentRemindersPage = ({ student }: StudentRemindersPageProps) => {
  const { slug } = useParams();
  const { user } = useAuth();
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800';
      case 'low': return 'text-green-600 bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800';
      default: return 'text-gray-600 bg-gray-50 border-gray-200 dark:bg-gray-900/20 dark:border-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'test': return <AlertTriangle className="w-5 h-5 text-red-600" />;
      case 'assignment': return <BookOpen className="w-5 h-5 text-blue-600" />;
      case 'meeting': return <Calendar className="w-5 h-5 text-purple-600" />;
      case 'library': return <BookOpen className="w-5 h-5 text-green-600" />;
      case 'permission': return <Bell className="w-5 h-5 text-orange-600" />;
      default: return <Bell className="w-5 h-5 text-gray-600" />;
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (date.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24 && diffInHours > 0) {
      return `Today at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else if (diffInHours < 48 && diffInHours > 24) {
      return `Tomorrow at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
    } else {
      return date.toLocaleDateString() + ' at ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  const getUrgencyLevel = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (date.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) return 'urgent';
    if (diffInHours < 72) return 'soon';
    return 'later';
  };

  const sortedReminders = mockReminders.sort((a, b) => {
    const dateA = new Date(a.dueDate);
    const dateB = new Date(b.dueDate);
    return dateA.getTime() - dateB.getTime();
  });

  const urgentReminders = sortedReminders.filter(r => getUrgencyLevel(r.dueDate) === 'urgent');
  const soonReminders = sortedReminders.filter(r => getUrgencyLevel(r.dueDate) === 'soon');
  const laterReminders = sortedReminders.filter(r => getUrgencyLevel(r.dueDate) === 'later');

  return (
    <div className="min-h-screen bg-[#f2f2f2] dark:bg-[#0d0d0d] pt-20 pb-24">
      <div className="max-w-6xl mx-auto px-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-black dark:text-white mb-2">
            Reminders
          </h1>
          <p className="text-black/60 dark:text-white/60">
            Stay on top of important deadlines and events
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-8 h-8 text-red-600" />
              <div>
                <h3 className="text-2xl font-bold text-black dark:text-white">
                  {urgentReminders.length}
                </h3>
                <p className="text-black/60 dark:text-white/60">Urgent (Today)</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10">
            <div className="flex items-center gap-3">
              <Clock className="w-8 h-8 text-yellow-600" />
              <div>
                <h3 className="text-2xl font-bold text-black dark:text-white">
                  {soonReminders.length}
                </h3>
                <p className="text-black/60 dark:text-white/60">This Week</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10">
            <div className="flex items-center gap-3">
              <Bell className="w-8 h-8 text-blue-600" />
              <div>
                <h3 className="text-2xl font-bold text-black dark:text-white">
                  {mockReminders.length}
                </h3>
                <p className="text-black/60 dark:text-white/60">Total Active</p>
              </div>
            </div>
          </div>
        </div>

        {/* Urgent Reminders */}
        {urgentReminders.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-red-600 mb-4 flex items-center gap-2">
              <AlertTriangle className="w-5 h-5" />
              Urgent - Due Today
            </h2>
            <div className="space-y-4">
              {urgentReminders.map((reminder) => (
                <div
                  key={reminder.id}
                  className="bg-red-50 dark:bg-red-900/20 rounded-xl p-6 border border-red-200 dark:border-red-800"
                >
                  <div className="flex items-start gap-4">
                    {getTypeIcon(reminder.type)}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-black dark:text-white mb-1">
                        {reminder.title}
                      </h3>
                      <p className="text-black/70 dark:text-white/70 mb-2">
                        {reminder.message}
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-black/60 dark:text-white/60">
                          {reminder.subject}
                        </span>
                        <span className="text-red-600 font-medium">
                          {formatDateTime(reminder.dueDate)}
                        </span>
                      </div>
                    </div>
                    <span className={cn(
                      "px-3 py-1 rounded-full text-xs font-medium border",
                      getPriorityColor(reminder.priority)
                    )}>
                      {reminder.priority.charAt(0).toUpperCase() + reminder.priority.slice(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Soon Reminders */}
        {soonReminders.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-yellow-600 mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Coming Up This Week
            </h2>
            <div className="space-y-4">
              {soonReminders.map((reminder) => (
                <div
                  key={reminder.id}
                  className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    {getTypeIcon(reminder.type)}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-black dark:text-white mb-1">
                        {reminder.title}
                      </h3>
                      <p className="text-black/60 dark:text-white/60 mb-2">
                        {reminder.message}
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-black/60 dark:text-white/60">
                          {reminder.subject}
                        </span>
                        <span className="text-black/60 dark:text-white/60">
                          {formatDateTime(reminder.dueDate)}
                        </span>
                      </div>
                    </div>
                    <span className={cn(
                      "px-3 py-1 rounded-full text-xs font-medium border",
                      getPriorityColor(reminder.priority)
                    )}>
                      {reminder.priority.charAt(0).toUpperCase() + reminder.priority.slice(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Later Reminders */}
        {laterReminders.length > 0 && (
          <div>
            <h2 className="text-xl font-semibold text-black dark:text-white mb-4 flex items-center gap-2">
              <Bell className="w-5 h-5" />
              Later This Month
            </h2>
            <div className="space-y-4">
              {laterReminders.map((reminder) => (
                <div
                  key={reminder.id}
                  className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10 hover:shadow-lg transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    {getTypeIcon(reminder.type)}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-black dark:text-white mb-1">
                        {reminder.title}
                      </h3>
                      <p className="text-black/60 dark:text-white/60 mb-2">
                        {reminder.message}
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <span className="text-black/60 dark:text-white/60">
                          {reminder.subject}
                        </span>
                        <span className="text-black/60 dark:text-white/60">
                          {formatDateTime(reminder.dueDate)}
                        </span>
                      </div>
                    </div>
                    <span className={cn(
                      "px-3 py-1 rounded-full text-xs font-medium border",
                      getPriorityColor(reminder.priority)
                    )}>
                      {reminder.priority.charAt(0).toUpperCase() + reminder.priority.slice(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentRemindersPage;
