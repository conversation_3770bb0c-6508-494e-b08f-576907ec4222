// components/student/StudentNewslettersPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';
import { Newspaper, Calendar, Tag, ExternalLink, Download } from 'lucide-react';
import { cn } from '../../lib/utils';
import { useState } from 'react';

interface Student {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
}

interface StudentNewslettersPageProps {
  student: Student;
}

// Mock data for newsletters - in a real app, this would come from the server
const mockNewsletters = [
  {
    id: '1',
    title: 'Weekly School Update - January 2024',
    description: 'Important updates about upcoming events, policy changes, and student achievements.',
    publishDate: '2024-01-22',
    category: 'school-news',
    author: 'Principal <PERSON>',
    isRead: false,
    isPinned: true,
    downloadUrl: '/newsletters/weekly-update-jan-2024.pdf',
    content: 'This week has been filled with exciting developments at our school. We are proud to announce that our debate team won the regional championship...'
  },
  {
    id: '2',
    title: 'Parent-Teacher Conference Schedule',
    description: 'Information about upcoming parent-teacher conferences and how to schedule appointments.',
    publishDate: '2024-01-20',
    category: 'events',
    author: 'School Administration',
    isRead: true,
    isPinned: false,
    downloadUrl: '/newsletters/ptc-schedule-jan-2024.pdf',
    content: 'Parent-teacher conferences are scheduled for the week of January 29th. Please use the online portal to book your appointments...'
  },
  {
    id: '3',
    title: 'Science Fair 2024 - Call for Participants',
    description: 'Details about this year\'s science fair, including themes, deadlines, and prizes.',
    publishDate: '2024-01-18',
    category: 'academics',
    author: 'Science Department',
    isRead: true,
    isPinned: false,
    downloadUrl: '/newsletters/science-fair-2024.pdf',
    content: 'We are excited to announce the 2024 Science Fair! This year\'s theme is "Innovation for Tomorrow". Students from all grades are encouraged to participate...'
  },
  {
    id: '4',
    title: 'Winter Sports Achievements',
    description: 'Celebrating our winter sports teams and their outstanding performances this season.',
    publishDate: '2024-01-15',
    category: 'sports',
    author: 'Athletic Department',
    isRead: false,
    isPinned: false,
    downloadUrl: '/newsletters/winter-sports-jan-2024.pdf',
    content: 'Our winter sports teams have been performing exceptionally well this season. The basketball team is currently undefeated...'
  },
  {
    id: '5',
    title: 'Library New Book Arrivals',
    description: 'Exciting new books and resources now available in the school library.',
    publishDate: '2024-01-12',
    category: 'library',
    author: 'Library Staff',
    isRead: true,
    isPinned: false,
    downloadUrl: '/newsletters/library-new-books-jan-2024.pdf',
    content: 'The library has received an exciting collection of new books this month, including the latest young adult fiction, science textbooks, and reference materials...'
  },
  {
    id: '6',
    title: 'Student Art Exhibition Opening',
    description: 'Join us for the opening of our annual student art exhibition featuring works from all grade levels.',
    publishDate: '2024-01-10',
    category: 'arts',
    author: 'Art Department',
    isRead: true,
    isPinned: false,
    downloadUrl: '/newsletters/art-exhibition-jan-2024.pdf',
    content: 'We are thrilled to invite you to the opening of our annual student art exhibition. This year\'s collection showcases incredible talent across all mediums...'
  }
];

const StudentNewslettersPage = ({ student }: StudentNewslettersPageProps) => {
  const { slug } = useParams();
  const { user } = useAuth();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedNewsletter, setSelectedNewsletter] = useState<string | null>(null);
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

  const categories = [
    { id: 'all', label: 'All Newsletters', count: mockNewsletters.length },
    { id: 'school-news', label: 'School News', count: mockNewsletters.filter(n => n.category === 'school-news').length },
    { id: 'events', label: 'Events', count: mockNewsletters.filter(n => n.category === 'events').length },
    { id: 'academics', label: 'Academics', count: mockNewsletters.filter(n => n.category === 'academics').length },
    { id: 'sports', label: 'Sports', count: mockNewsletters.filter(n => n.category === 'sports').length },
    { id: 'arts', label: 'Arts', count: mockNewsletters.filter(n => n.category === 'arts').length },
    { id: 'library', label: 'Library', count: mockNewsletters.filter(n => n.category === 'library').length }
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'school-news': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'events': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'academics': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'sports': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      case 'arts': return 'bg-pink-100 text-pink-800 dark:bg-pink-900/20 dark:text-pink-400';
      case 'library': return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  const filteredNewsletters = selectedCategory === 'all' 
    ? mockNewsletters 
    : mockNewsletters.filter(newsletter => newsletter.category === selectedCategory);

  const sortedNewsletters = filteredNewsletters.sort((a, b) => {
    // Pinned items first, then by date (newest first)
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    return new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime();
  });

  const unreadCount = mockNewsletters.filter(n => !n.isRead).length;
  const pinnedCount = mockNewsletters.filter(n => n.isPinned).length;

  const selectedNewsletterData = selectedNewsletter 
    ? mockNewsletters.find(n => n.id === selectedNewsletter)
    : null;

  return (
    <div className="min-h-screen bg-[#f2f2f2] dark:bg-[#0d0d0d] pt-20 pb-24">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-black dark:text-white mb-2">
            Newsletters
          </h1>
          <p className="text-black/60 dark:text-white/60">
            Stay updated with school news, events, and important announcements
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Stats */}
            <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10 mb-6">
              <h3 className="text-lg font-semibold text-black dark:text-white mb-4">
                Newsletter Stats
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-black/60 dark:text-white/60">Total</span>
                  <span className="font-semibold text-black dark:text-white">{mockNewsletters.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-black/60 dark:text-white/60">Unread</span>
                  <span className="font-semibold text-blue-600">{unreadCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-black/60 dark:text-white/60">Pinned</span>
                  <span className="font-semibold text-yellow-600">{pinnedCount}</span>
                </div>
              </div>
            </div>

            {/* Categories */}
            <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10">
              <h3 className="text-lg font-semibold text-black dark:text-white mb-4">
                Categories
              </h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={cn(
                      "w-full text-left px-3 py-2 rounded-lg transition-colors flex items-center justify-between",
                      selectedCategory === category.id
                        ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20'
                        : 'text-black/60 dark:text-white/60 hover:bg-black/5 dark:hover:bg-white/5'
                    )}
                  >
                    <span>{category.label}</span>
                    <span className="text-xs bg-black/10 dark:bg-white/10 px-2 py-1 rounded-full">
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Newsletter List/Detail */}
          <div className="lg:col-span-3">
            {selectedNewsletterData ? (
              /* Newsletter Detail View */
              <div className="bg-white dark:bg-[#1a1a1a] rounded-xl border border-black/10 dark:border-white/10">
                {/* Newsletter Header */}
                <div className="p-6 border-b border-black/10 dark:border-white/10">
                  <div className="flex items-start justify-between mb-4">
                    <button
                      onClick={() => setSelectedNewsletter(null)}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                    >
                      ← Back to Newsletters
                    </button>
                    <div className="flex items-center gap-2">
                      <a
                        href={selectedNewsletterData.downloadUrl}
                        className="flex items-center gap-1 text-blue-600 hover:text-blue-700 text-sm"
                      >
                        <Download className="w-4 h-4" />
                        Download PDF
                      </a>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 mb-3">
                    <span className={cn(
                      "px-2 py-1 rounded-full text-xs font-medium",
                      getCategoryColor(selectedNewsletterData.category)
                    )}>
                      {categories.find(c => c.id === selectedNewsletterData.category)?.label}
                    </span>
                    {selectedNewsletterData.isPinned && (
                      <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                        Pinned
                      </span>
                    )}
                  </div>
                  
                  <h2 className="text-2xl font-bold text-black dark:text-white mb-2">
                    {selectedNewsletterData.title}
                  </h2>
                  
                  <div className="flex items-center gap-4 text-sm text-black/60 dark:text-white/60">
                    <span>By {selectedNewsletterData.author}</span>
                    <span>{formatDate(selectedNewsletterData.publishDate)}</span>
                  </div>
                </div>
                
                {/* Newsletter Content */}
                <div className="p-6">
                  <div className="prose prose-gray dark:prose-invert max-w-none">
                    <p className="text-lg text-black/80 dark:text-white/80 mb-6">
                      {selectedNewsletterData.description}
                    </p>
                    <div className="whitespace-pre-line text-black/70 dark:text-white/70 leading-relaxed">
                      {selectedNewsletterData.content}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              /* Newsletter List View */
              <div className="space-y-4">
                {sortedNewsletters.map((newsletter) => (
                  <div
                    key={newsletter.id}
                    onClick={() => setSelectedNewsletter(newsletter.id)}
                    className={cn(
                      "bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10 cursor-pointer hover:shadow-lg transition-all",
                      !newsletter.isRead && "border-l-4 border-l-blue-500",
                      newsletter.isPinned && "ring-2 ring-yellow-200 dark:ring-yellow-800"
                    )}
                  >
                    <div className="flex items-start gap-4">
                      <Newspaper className={cn(
                        "w-6 h-6 mt-1 flex-shrink-0",
                        newsletter.isRead ? "text-gray-400" : "text-blue-600"
                      )} />
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className={cn(
                            "text-lg font-semibold",
                            newsletter.isRead 
                              ? "text-black/80 dark:text-white/80" 
                              : "text-black dark:text-white"
                          )}>
                            {newsletter.title}
                          </h3>
                          <div className="flex items-center gap-2 ml-4">
                            {newsletter.isPinned && (
                              <Tag className="w-4 h-4 text-yellow-500" />
                            )}
                            <span className="text-sm text-black/60 dark:text-white/60 whitespace-nowrap">
                              {formatDate(newsletter.publishDate)}
                            </span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 mb-3">
                          <span className={cn(
                            "px-2 py-1 rounded-full text-xs font-medium",
                            getCategoryColor(newsletter.category)
                          )}>
                            {categories.find(c => c.id === newsletter.category)?.label}
                          </span>
                          <span className="text-sm text-black/60 dark:text-white/60">
                            By {newsletter.author}
                          </span>
                        </div>
                        
                        <p className={cn(
                          "text-sm line-clamp-2",
                          newsletter.isRead 
                            ? "text-black/60 dark:text-white/60" 
                            : "text-black/80 dark:text-white/80"
                        )}>
                          {newsletter.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {sortedNewsletters.length === 0 && (
                  <div className="text-center py-12">
                    <Newspaper className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
                      No newsletters found
                    </h3>
                    <p className="text-black/60 dark:text-white/60">
                      No newsletters available in this category
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentNewslettersPage;
