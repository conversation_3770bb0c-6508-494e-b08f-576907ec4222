'use client';
import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { cn } from '../../../../lib/utils';
import { useIsMobile } from '../../../../hooks/use-mobile';
import { useSharedSidebarState, useSharedRightFlyoutState } from '../../../../stores/sidebar-store.client';
import { SquircleProvider } from '../../../providers/squircle-provider.client';
import {
  SidebarProvider,
  SidebarInset,
} from '../../../ui/sidebar.client';
import { StudentTopNav } from './student-top-nav.client';
import { RightSidebar } from '../../shared/dual-sidebar/right-sidebar';
import { UserNav } from '../../shared/dual-sidebar/user-nav.client';
import { useLocation } from 'blade/hooks';

interface EnhancedSidebarStudentProps {
  children: React.ReactNode;
  className?: string;
  showUserNav?: boolean;
  showTopNav?: boolean;
}

export function EnhancedSidebarStudent({
  children,
  className,
  showUserNav = true,
  showTopNav = true
}: EnhancedSidebarStudentProps) {
  const isMobile = useIsMobile();
  const location = useLocation();
  const {
    isRightSidebarOpen,
    activeFlyout,
    isRightFlyoutOpen,
    rightSidebarContent,
    updateActiveFlyout,
    updateRightSidebarContent,
    toggleRightSidebar,
    clearAllSidebarStatesOnce,
  } = useSharedSidebarState();

  // Flyout state (no longer used to hide UserNav)
  const { isOpen: flyoutOpen } = useSharedRightFlyoutState();

  // Note: Removed automatic sidebar clearing to prevent loops
  // Sidebar states will be managed by user interactions instead

  // Calculate main content styles based on sidebar states - FASTER TRANSITIONS
  const mainContentStyles = useMemo(() => {
    const styles: Record<string, string> = {
      "transition-all": "true",
      "duration-100": "true", // FASTER: 100ms instead of 300ms
      "ease-out": "true", // FASTER: ease-out instead of ease-in-out
      "flex-1": "true",
      "h-screen": "true",
    };

    // Left flyout adjustments - only when hamburger menu flyout is open
    if (!isMobile && activeFlyout) {
      styles["ml-[284px]"] = "true"; // Flyout width (284px)
    }

    // Right sidebar adjustments - ONLY for sidebar, NOT flyouts (flyouts should be overlays)
    if (!isMobile && isRightSidebarOpen) {
      styles["mr-[350px]"] = "true"; // Just sidebar - flyouts are overlays
    } else if (isMobile && isRightSidebarOpen) {
      styles["mr-[90vw]"] = "true";
      styles["sm:mr-[350px]"] = "true";
    }

    return cn(styles);
  }, [isMobile, isRightSidebarOpen, activeFlyout]);

  // Top navigation styles calculation - FASTER TRANSITIONS
  const topNavStyles = useMemo(() => {
    const styles: Record<string, string> = {
      "fixed": "true",
      "top-0": "true",
      "left-0": "true",
      "right-0": "true",
      "h-16": "true",
      "z-50": "true",
      "transition-all": "true",
      "duration-100": "true", // FASTER: 100ms instead of 200ms
      "ease-out": "true",
      "border-b": "true",
      "border-black/10": "true",
      "dark:border-white/10": "true",
    };

    // Left flyout adjustments
    if (!isMobile && activeFlyout) {
      styles["ml-[284px]"] = "true"; // Flyout width (284px)
    }

    // Right sidebar adjustments - ONLY for sidebar, NOT flyouts
    if (!isMobile && isRightSidebarOpen) {
      styles["mr-[350px]"] = "true"; // Just sidebar - flyouts are overlays
    }

    return cn(styles);
  }, [isMobile, isRightSidebarOpen, activeFlyout]);

  return (
    <SquircleProvider>
      
        <SidebarProvider
          defaultOpenLeft={false}
          defaultOpenRight={false}
          className="min-h-screen bg-fixed bg-gradient-to-r from-[#f2f2f2] via-[#e8e8e8] to-[#eeeeee] dark:from-[#101012] dark:via-[#18181a] dark:to-[#171719]"
        >
        <div className="flex h-screen w-full">
          {/* Top Navigation - Horizontal layout */}
          {showTopNav && (
            <div className={topNavStyles}>
              <StudentTopNav
                flyout={activeFlyout}
                setFlyout={updateActiveFlyout}
              />
            </div>
          )}

          {/* User Navigation - Fixed position top-right */}
          {showUserNav && (
            <UserNav />
          )}

          {/* Main Content Area - Single scrollable container */}
          <SidebarInset className={cn("flex-1 rounded-b-xl", mainContentStyles)}>

          <main className="pt-16 overflow-y-auto custom-scrollbar h-full">
            {children}
          </main>
          </SidebarInset>

          {/* Right Sidebar */}
          {rightSidebarContent && (
            <RightSidebar
              content={rightSidebarContent}
            />
          )}
        </div>
        </SidebarProvider>
      
    </SquircleProvider>
  );
}
