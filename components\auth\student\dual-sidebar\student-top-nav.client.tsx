'use client';
import React, { useState, useCallback, useRef, useEffect } from "react";
import { motion, AnimatePresence } from 'motion/react';
import { cn } from "../../../../lib/utils";
import { useIsMobile } from "../../../../hooks/use-mobile";

import { Link, Image } from 'blade/client/components';
import { useLocation, useParams, useRedirect } from 'blade/hooks';
import useMeasure from 'react-use-measure';
import {
  Menu,
  Home,
  FileText,
  Users,
  Calendar,
  Award,
  MessageSquare,
  BookOpen,
  Settings2,
} from "lucide-react";

interface StudentTopNavProps {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
}

// Student-specific navigation items for mobile toolbar
interface StudentNavItem {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  tooltip: string;
  type: "flyout" | "link";
  href?: string;
  section: number;
}

const studentNavItems: StudentNavItem[] = [
  // Hamburger menu - opens flyout (only for mobile)
  {
    id: "menu",
    icon: Menu,
    label: "Menu",
    tooltip: "Navigation Menu",
    type: "flyout" as const,
    section: 1
  },
  // Navigation items that redirect to pages
  {
    id: "todo",
    icon: Home,
    label: "To do",
    tooltip: "Assignments to do",
    type: "link" as const,
    href: "to-do",
    section: 2
  },
  {
    id: "reminders",
    icon: FileText,
    label: "Reminders",
    tooltip: "A reminder of assignment",
    type: "link" as const,
    href: "reminders",
    section: 2
  },
  {
    id: "inbox",
    icon: MessageSquare,
    label: "Inbox",
    tooltip: "Messages",
    type: "link" as const,
    href: "inbox",
    section: 2
  },
  {
    id: "newsletters",
    icon: BookOpen,
    label: "Newsletters",
    tooltip: "Newsletters",
    type: "link" as const,
    href: "newsletters",
    section: 2
  },
];

// Flyout menu items for hamburger menu
const flyoutMenuItems = [
  {
    title: "Quick Actions",
    items: [
      { name: "Submit Assignment", href: "/student/assignments/submit", icon: FileText },
      { name: "Join Class", href: "/student/classes/join", icon: Users },
      { name: "View Schedule", href: "/student/schedule", icon: Calendar },
      { name: "Check Grades", href: "/student/grades", icon: Award },
    ]
  },
  {
    title: "Resources",
    items: [
      { name: "Library", href: "/student/library", icon: BookOpen },
      { name: "Study Groups", href: "/student/study-groups", icon: Users },
      { name: "Help Center", href: "/student/help", icon: MessageSquare },
      { name: "Settings", href: "/student/settings", icon: Settings2 },
    ]
  }
];

// Mobile Horizontal Toolbar Component (similar to teacher/school pattern)
function MobileHorizontalToolbar({
  flyout,
  handleToggleFlyout
}: {
  flyout: string | null;
  setFlyout: (flyout: string | null) => void;
  handleToggleFlyout: (itemId: string) => void;
}) {
  const [active, setActive] = useState<string | null>(null);
  const [contentRef, { height: heightContent }] = useMeasure();
  const ref = useRef<HTMLDivElement>(null);
  const [isOpen, setIsOpen] = useState(false);
  const [maxWidth, setMaxWidth] = useState(0);

  // Add hooks for navigation
  const params = useParams();
  const location = useLocation();
  const redirect = useRedirect();

  // Sync isOpen with flyout state
  useEffect(() => {
    if (flyout === 'menu') {
      setIsOpen(true);
      setActive('menu');
    } else {
      // Delay closing to allow animation to complete
      const timer = setTimeout(() => {
        setIsOpen(false);
        setActive(null);
      }, 50); // Small delay to ensure smooth transition
      return () => clearTimeout(timer);
    }
  }, [flyout]);

  // Calculate max width for container
  useEffect(() => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      setMaxWidth(rect.width - 120); // Account for padding and menu button
    }
  }, []);

  // Get navigation items for section 2 (main navigation)
  const navigationItems = studentNavItems.filter(item => item.section === 2);

  // Determine current active item based on pathname
  const getCurrentActiveItem = () => {
    const currentPath = location.pathname;

    // Find matching navigation item by checking if the path includes the href
    const matchingItem = navigationItems.find(item =>
      item.href && currentPath.includes(`/${item.href}`)
    );

    return matchingItem?.id || null;
  };

  const [activeItem, setActiveItem] = useState(getCurrentActiveItem());

  // Update active item when location changes
  useEffect(() => {
    setActiveItem(getCurrentActiveItem());
  }, [location.pathname]);

  const handleItemClick = (item: StudentNavItem) => {
    // For navigation items, construct full URL with student slug
    if (item.type === 'link' && item.href) {
      const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
      const fullUrl = item.href.startsWith('/')
        ? item.href
        : `/student/${slug}/${item.href}`;
      redirect(fullUrl);
      return;
    }
  };

  return (
    <div className="fixed bottom-2 left-1/2 w-[96vw] transform -translate-x-1/2 z-[100000004]">
      <div className={cn(
        "bg-gradient-to-b from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]",
        "overflow-hidden",
        isOpen ? "rounded-xl" : "rounded-full"
      )}>
        {/* Expanded Content - Shows above the toolbar */}
        <AnimatePresence initial={false} mode='sync'>
          {isOpen ? (
            <motion.div
              key='content'
              initial={{ height: 0 }}
              animate={{ height: heightContent || 0 }}
              exit={{ height: 0 }}
              style={{
                width: maxWidth,
              } as React.CSSProperties}
            >
              <div ref={contentRef} className="p-4 max-h-[80vh] overflow-y-auto">
                <div className="space-y-6">
                  {flyoutMenuItems.map((section, sectionIndex) => (
                    <motion.div
                      key={sectionIndex}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: active === 'menu' ? 1 : 0 }}
                      exit={{ opacity: 0 }}
                      className={cn(
                        'space-y-2',
                        active === 'menu' ? 'block' : 'hidden'
                      )}
                    >
                      <h3 className="text-sm font-medium text-black/70 dark:text-white/70 uppercase tracking-wide">
                        {section.title}
                      </h3>
                      <div className="space-y-1">
                        {section.items.map((item, itemIndex) => {
                          const IconComponent = item.icon;
                          return (
                            <Link
                              key={itemIndex}
                              href={item.href}
                            >
                              <div className="flex items-center gap-3 p-3 rounded-md hover:bg-black/5 dark:hover:bg-white/5 transition-colors group">
                                <IconComponent className="w-4 h-4 text-black/60 dark:text-white/60 group-hover:text-black dark:group-hover:text-white transition-colors" />
                                <span className="text-sm font-medium text-black/80 dark:text-white/80 group-hover:text-black dark:group-hover:text-white transition-colors">
                                  {item.name}
                                </span>
                              </div>
                            </Link>
                          );
                        })}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          ) : null}
        </AnimatePresence>

        {/* Toolbar - Always visible */}
        <div className="flex items-center justify-between px-4 h-16" ref={ref}>
          {/* Left Section - Hamburger Menu */}
          <div className="flex items-center">
            <button
              onClick={() => handleToggleFlyout('menu')}
              className={cn(
                "relative w-9 h-9 flex items-center justify-center rounded-full transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800 focus-visible:ring-2 active:scale-[0.98]",
                "touch-manipulation select-none outline-none",
                flyout === 'menu'
                  ? 'bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]'
                  : 'text-zinc-500 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-200'
              )}
              type="button"
            >
              <Menu className="w-5 h-5" />
            </button>
          </div>

          {/* Center Section - Text-based Navigation */}
          <div className="flex-1 flex items-center justify-center">
            <div
              className="flex items-center gap-1 overflow-x-auto scrollbar-hide"
              style={{ maxWidth: maxWidth } as React.CSSProperties}
            >
              {navigationItems.map((item) => {
                const isActive = activeItem === item.id;
                const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
                const fullUrl = item.href?.startsWith('/')
                  ? item.href
                  : `/student/${slug}/${item.href}`;

                return (
                  <Link key={item.id} href={fullUrl || '#'}>
                    <button
                      onClick={() => handleItemClick(item)}
                      className={cn(
                        "relative flex h-9 px-4 items-center justify-center rounded-full transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800 focus-visible:ring-2 active:scale-[0.98] whitespace-nowrap",
                        "touch-manipulation select-none outline-none text-sm font-medium",
                        isActive
                          ? 'bg-gradient-to-br from-zinc-100 to-zinc-200 text-zinc-800 shadow-[0_-1px_0_1px_rgba(255,255,255,0.8)_inset,0_0_0_1px_rgb(220,220,220)_inset,0_0.5px_0_1.5px_#ccc_inset] dark:bg-gradient-to-b dark:from-[#212026] dark:via-[#212026] dark:to-[#29282e] dark:text-zinc-50 dark:shadow-[0_-1px_0_1px_rgba(0,0,0,0.8)_inset,0_0_0_1px_rgb(9_9_11)_inset,0_0.5px_0_1.5px_#71717a_inset]'
                          : 'text-zinc-500 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-200'
                      )}
                      type="button"
                    >
                      {item.label}
                    </button>
                  </Link>
                );
              })}
            </div>
          </div>

          {/* Right Section - Empty (no UserNav as requested) */}
          <div className="flex items-center w-9">
            {/* Empty space to balance the layout */}
          </div>
        </div>
      </div>
    </div>
  );
}

// Desktop Top Navigation Component
function DesktopTopNavigation({
  flyout,
  handleToggleFlyout
}: {
  flyout: string | null;
  handleToggleFlyout: (itemId: string) => void;
}) {
  const location = useLocation();
  const params = useParams();

  // Get menu item (section 1) and navigation items (section 2)
  const menuItem = studentNavItems.find(item => item.section === 1);
  const navigationItems = studentNavItems.filter(item => item.section === 2);

  // Determine current active item based on pathname
  const getCurrentActiveItem = () => {
    const currentPath = location.pathname;

    // Find matching navigation item by checking if the path includes the href
    const matchingItem = navigationItems.find(item =>
      item.href && currentPath.includes(`/${item.href}`)
    );

    return matchingItem?.id || null;
  };

  const [activeItem, setActiveItem] = useState(getCurrentActiveItem());

  // Update active item when location changes
  useEffect(() => {
    setActiveItem(getCurrentActiveItem());
  }, [location.pathname]);

  return (
    <div className={cn(
      "fixed top-0 right-0 h-16 z-50 bg-[#f2f2f2] dark:bg-[#0d0d0d] border-b border-black/10 dark:border-white/10 transition-all duration-300 ease-in-out",
      flyout === 'menu' ? "left-[284px]" : "left-0"
    )}>
      <div className="flex items-center h-full px-6">
        {/* Logo Section - Fixed position, always visible */}
        <div className="fixed top-4 left-4 z-60 flex items-center">
          <Image
            src="/logo-lightmode.png"
            alt="Penned Logo"
            className="w-8 h-8 dark:hidden"
            width={32}
            height={32}
          />
          <Image
            src="/logo-darkmode.png"
            alt="Penned Logo"
            className="w-8 h-8 hidden dark:block"
            width={32}
            height={32}
          />
        </div>

        {/* Menu Button - Positioned after logo space */}
        {menuItem && (
          <button
            onClick={() => handleToggleFlyout(menuItem.id)}
            className={cn(
              "relative flex h-10 px-4 items-center justify-center rounded-lg transition-colors hover:bg-black/5 dark:hover:bg-white/5 focus-visible:ring-2 active:scale-[0.98] mr-6 ml-12",
              "touch-manipulation select-none outline-none text-sm font-medium whitespace-nowrap",
              flyout === menuItem.id
                ? 'text-black dark:text-white bg-black/5 dark:bg-white/5'
                : 'text-black/60 dark:text-white/60 hover:text-black/80 dark:hover:text-white/80'
            )}
            type="button"
          >
            <menuItem.icon className="w-4 h-4 mr-2" />
            {menuItem.label}
          </button>
        )}

        {/* Navigation Items */}
        <div className="flex items-center gap-6">
          {navigationItems.map((item) => {
            const isActive = activeItem === item.id;
            const slug = Array.isArray(params['slug']) ? params['slug'][0] : params['slug'];
            const fullUrl = item.href?.startsWith('/')
              ? item.href
              : `/student/${slug}/${item.href}`;

            return (
              <Link key={item.id} href={fullUrl || '#'}>
                <button
                  className={cn(
                    "relative flex h-10 px-4 items-center justify-center rounded-lg transition-colors hover:bg-black/5 dark:hover:bg-white/5 focus-visible:ring-2 active:scale-[0.98]",
                    "touch-manipulation select-none outline-none text-sm font-medium whitespace-nowrap",
                    isActive
                      ? 'text-black dark:text-white bg-black/5 dark:bg-white/5'
                      : 'text-black/60 dark:text-white/60 hover:text-black/80 dark:hover:text-white/80'
                  )}
                  type="button"
                >
                  {item.label}
                </button>
              </Link>
            );
          })}
        </div>
      </div>
    </div>
  );
}

export function StudentTopNav({
  flyout,
  setFlyout
}: StudentTopNavProps) {
  const isMobile = useIsMobile();

  // Simplified toggle handler without debouncing - matches working right-sidebar pattern
  const handleToggleFlyout = useCallback((itemId: string) => {
    console.log(`🎯 Toggle flyout for ${itemId}:`, { currentFlyout: flyout, itemId });
    const newFlyout = flyout === itemId ? null : itemId;
    console.log(`🎯 Setting flyout to:`, newFlyout);
    setFlyout(newFlyout);
  }, [flyout, setFlyout]);

  return (
    <div className="flex h-full relative">
      {/* Desktop: Top Navigation */}
      {!isMobile && (
        <DesktopTopNavigation
          flyout={flyout}
          handleToggleFlyout={handleToggleFlyout}
        />
      )}

      {/* Mobile: Bottom Toolbar */}
      {isMobile && (
        <MobileHorizontalToolbar
          flyout={flyout}
          setFlyout={setFlyout}
          handleToggleFlyout={handleToggleFlyout}
        />
      )}

      {/* Desktop Flyout Panel for Hamburger Menu - Full Height */}
      {!isMobile && (
        <div
          className={cn(
            "fixed left-0 top-0 h-screen w-[284px] bg-[#f2f2f2] dark:bg-[#0d0d0d] border-r border-black/5 dark:border-white/5 shadow-xl z-50 flex flex-col",
            "transition-all duration-300 ease-in-out",
            flyout === 'menu' ? "opacity-100 translate-x-0" : "opacity-0 -translate-x-full pointer-events-none"
          )}
        >
          {/* Desktop flyout content - with top padding for logo space */}
          {flyout === 'menu' && (
            <div className="pt-16 h-full overflow-y-auto">
              {renderFlyoutContent(flyout)}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

function renderFlyoutContent(flyout: string | null) {
  if (flyout !== 'menu') return null;

  return (
    <div className="flex flex-col h-full">
      {/* Header Section */}
      <div className="relative h-16">
        {/* Static border - appears instantly */}
        <div className="absolute bottom-0 left-0 right-0 border-b border-black/20 dark:border-white/20" />

        {/* Header content area */}
        <div className="p-4 flex items-center h-full">
          {/* Title - positioned to align with fixed logo */}
          <h2 className="text-xl font-semibold text-black dark:text-white ml-12">
            Student Menu
          </h2>
        </div>
      </div>

      {/* Content Section */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="space-y-6">
          {flyoutMenuItems.map((section, sectionIndex) => (
            <div key={sectionIndex} className="space-y-2">
              <h3 className="text-sm font-medium text-black/70 dark:text-white/70 uppercase tracking-wide">
                {section.title}
              </h3>
              <div className="space-y-1">
                {section.items.map((item, itemIndex) => {
                  const IconComponent = item.icon;
                  return (
                    <Link
                      key={itemIndex}
                      href={item.href}
                    >
                      <div className="flex items-center gap-3 p-3 rounded-md hover:bg-black/5 dark:hover:bg-white/5 transition-colors group">
                        <IconComponent className="w-4 h-4 text-black/60 dark:text-white/60 group-hover:text-black dark:group-hover:text-white transition-colors" />
                        <span className="text-sm font-medium text-black/80 dark:text-white/80 group-hover:text-black dark:group-hover:text-white transition-colors">
                          {item.name}
                        </span>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}