// components/student/StudentInboxPage.client.tsx
'use client';

import { useParams } from 'blade/hooks';
import { useAuth } from '../../hooks/useAuth';
import { Mail, MailOpen, Star, Archive, Trash2, User, Calendar } from 'lucide-react';
import { cn } from '../../lib/utils';
import { useState } from 'react';

interface Student {
  id: string;
  name: string;
  email: string;
  slug: string;
  role: string;
}

interface StudentInboxPageProps {
  student: Student;
}

// Mock data for messages - in a real app, this would come from the server
const mockMessages = [
  {
    id: '1',
    from: 'Mrs. <PERSON>',
    fromEmail: '<EMAIL>',
    subject: 'Great work on your math test!',
    preview: 'I wanted to congratulate you on your excellent performance on the algebra test. You scored 95%!',
    content: 'Dear Student,\n\nI wanted to congratulate you on your excellent performance on the algebra test. You scored 95%! Your hard work and dedication are really showing. Keep up the great work!\n\nBest regards,\nMrs. Johnson',
    timestamp: '2024-01-24T10:30:00',
    isRead: false,
    isStarred: true,
    type: 'teacher'
  },
  {
    id: '2',
    from: 'School Administration',
    fromEmail: '<EMAIL>',
    subject: 'Field Trip Permission Slip Reminder',
    preview: 'This is a reminder to return your signed permission slip for the upcoming museum visit.',
    content: 'Dear Student and Parents,\n\nThis is a reminder to return your signed permission slip for the upcoming museum visit scheduled for next Friday. Please bring the completed form to the main office by Thursday.\n\nThank you,\nSchool Administration',
    timestamp: '2024-01-23T14:15:00',
    isRead: true,
    isStarred: false,
    type: 'admin'
  },
  {
    id: '3',
    from: 'Mr. Davis',
    fromEmail: '<EMAIL>',
    subject: 'Science Lab Report Feedback',
    preview: 'I have reviewed your photosynthesis lab report. Overall, excellent work with some suggestions for improvement.',
    content: 'Dear Student,\n\nI have reviewed your photosynthesis lab report. Overall, excellent work! Your observations were detailed and your conclusions were well-supported by the data.\n\nA few suggestions for improvement:\n- Include more detailed measurements in your data table\n- Consider discussing potential sources of error\n\nGrade: B+\n\nKeep up the good work!\nMr. Davis',
    timestamp: '2024-01-22T16:45:00',
    isRead: true,
    isStarred: false,
    type: 'teacher'
  },
  {
    id: '4',
    from: 'Library',
    fromEmail: '<EMAIL>',
    subject: 'Book Return Reminder',
    preview: 'Your book "To Kill a Mockingbird" is due for return tomorrow. Please return it to avoid late fees.',
    content: 'Dear Student,\n\nThis is a friendly reminder that your book "To Kill a Mockingbird" is due for return tomorrow (January 25th). Please return it to the library to avoid late fees.\n\nIf you need to renew the book, please visit the library or contact us.\n\nThank you,\nSchool Library',
    timestamp: '2024-01-21T09:00:00',
    isRead: false,
    isStarred: false,
    type: 'library'
  },
  {
    id: '5',
    from: 'Ms. Rodriguez',
    fromEmail: '<EMAIL>',
    subject: 'English Essay Extension Approved',
    preview: 'Your request for an extension on the English essay has been approved. New due date is February 5th.',
    content: 'Dear Student,\n\nYour request for an extension on the English essay "Analysis of To Kill a Mockingbird" has been approved. The new due date is February 5th.\n\nPlease use this extra time wisely to produce your best work. Remember to focus on:\n- Character development\n- Themes and symbolism\n- Historical context\n\nIf you need any additional help, please don\'t hesitate to ask.\n\nBest regards,\nMs. Rodriguez',
    timestamp: '2024-01-20T11:20:00',
    isRead: true,
    isStarred: true,
    type: 'teacher'
  }
];

const StudentInboxPage = ({ student }: StudentInboxPageProps) => {
  const { slug } = useParams();
  const { user } = useAuth();
  const [selectedMessage, setSelectedMessage] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | 'unread' | 'starred'>('all');
  
  // Verify the slug matches the current user
  if (user && user.slug !== slug) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You can only access your own profile.</p>
        </div>
      </div>
    );
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'teacher': return <User className="w-4 h-4 text-blue-600" />;
      case 'admin': return <Calendar className="w-4 h-4 text-purple-600" />;
      case 'library': return <Archive className="w-4 h-4 text-green-600" />;
      default: return <Mail className="w-4 h-4 text-gray-600" />;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const filteredMessages = mockMessages.filter(message => {
    switch (filter) {
      case 'unread': return !message.isRead;
      case 'starred': return message.isStarred;
      default: return true;
    }
  });

  const unreadCount = mockMessages.filter(m => !m.isRead).length;
  const starredCount = mockMessages.filter(m => m.isStarred).length;

  const selectedMessageData = selectedMessage 
    ? mockMessages.find(m => m.id === selectedMessage)
    : null;

  return (
    <div className="min-h-screen bg-[#f2f2f2] dark:bg-[#0d0d0d] pt-20 pb-24">
      <div className="max-w-7xl mx-auto px-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-black dark:text-white mb-2">
            Inbox
          </h1>
          <p className="text-black/60 dark:text-white/60">
            Messages from teachers, administration, and school services
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Stats */}
            <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10 mb-6">
              <h3 className="text-lg font-semibold text-black dark:text-white mb-4">
                Message Summary
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-black/60 dark:text-white/60">Total Messages</span>
                  <span className="font-semibold text-black dark:text-white">{mockMessages.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-black/60 dark:text-white/60">Unread</span>
                  <span className="font-semibold text-red-600">{unreadCount}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-black/60 dark:text-white/60">Starred</span>
                  <span className="font-semibold text-yellow-600">{starredCount}</span>
                </div>
              </div>
            </div>

            {/* Filters */}
            <div className="bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10">
              <h3 className="text-lg font-semibold text-black dark:text-white mb-4">
                Filter Messages
              </h3>
              <div className="space-y-2">
                <button
                  onClick={() => setFilter('all')}
                  className={cn(
                    "w-full text-left px-3 py-2 rounded-lg transition-colors",
                    filter === 'all'
                      ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20'
                      : 'text-black/60 dark:text-white/60 hover:bg-black/5 dark:hover:bg-white/5'
                  )}
                >
                  All Messages ({mockMessages.length})
                </button>
                <button
                  onClick={() => setFilter('unread')}
                  className={cn(
                    "w-full text-left px-3 py-2 rounded-lg transition-colors",
                    filter === 'unread'
                      ? 'bg-red-50 text-red-600 dark:bg-red-900/20'
                      : 'text-black/60 dark:text-white/60 hover:bg-black/5 dark:hover:bg-white/5'
                  )}
                >
                  Unread ({unreadCount})
                </button>
                <button
                  onClick={() => setFilter('starred')}
                  className={cn(
                    "w-full text-left px-3 py-2 rounded-lg transition-colors",
                    filter === 'starred'
                      ? 'bg-yellow-50 text-yellow-600 dark:bg-yellow-900/20'
                      : 'text-black/60 dark:text-white/60 hover:bg-black/5 dark:hover:bg-white/5'
                  )}
                >
                  Starred ({starredCount})
                </button>
              </div>
            </div>
          </div>

          {/* Message List */}
          <div className="lg:col-span-2">
            {selectedMessageData ? (
              /* Message Detail View */
              <div className="bg-white dark:bg-[#1a1a1a] rounded-xl border border-black/10 dark:border-white/10">
                {/* Message Header */}
                <div className="p-6 border-b border-black/10 dark:border-white/10">
                  <div className="flex items-start justify-between mb-4">
                    <button
                      onClick={() => setSelectedMessage(null)}
                      className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                    >
                      ← Back to Inbox
                    </button>
                    <div className="flex items-center gap-2">
                      {selectedMessageData.isStarred && (
                        <Star className="w-4 h-4 text-yellow-500 fill-current" />
                      )}
                      {getTypeIcon(selectedMessageData.type)}
                    </div>
                  </div>
                  <h2 className="text-xl font-semibold text-black dark:text-white mb-2">
                    {selectedMessageData.subject}
                  </h2>
                  <div className="flex items-center gap-4 text-sm text-black/60 dark:text-white/60">
                    <span>From: {selectedMessageData.from}</span>
                    <span>{formatTimestamp(selectedMessageData.timestamp)}</span>
                  </div>
                </div>
                
                {/* Message Content */}
                <div className="p-6">
                  <div className="whitespace-pre-line text-black/80 dark:text-white/80 leading-relaxed">
                    {selectedMessageData.content}
                  </div>
                </div>
              </div>
            ) : (
              /* Message List View */
              <div className="space-y-4">
                {filteredMessages.map((message) => (
                  <div
                    key={message.id}
                    onClick={() => setSelectedMessage(message.id)}
                    className={cn(
                      "bg-white dark:bg-[#1a1a1a] rounded-xl p-6 border border-black/10 dark:border-white/10 cursor-pointer hover:shadow-lg transition-all",
                      !message.isRead && "border-l-4 border-l-blue-500"
                    )}
                  >
                    <div className="flex items-start gap-4">
                      <div className="flex items-center gap-2">
                        {message.isRead ? (
                          <MailOpen className="w-5 h-5 text-gray-400" />
                        ) : (
                          <Mail className="w-5 h-5 text-blue-600" />
                        )}
                        {getTypeIcon(message.type)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-1">
                          <h3 className={cn(
                            "font-semibold truncate",
                            message.isRead 
                              ? "text-black/80 dark:text-white/80" 
                              : "text-black dark:text-white"
                          )}>
                            {message.subject}
                          </h3>
                          <div className="flex items-center gap-2 ml-4">
                            {message.isStarred && (
                              <Star className="w-4 h-4 text-yellow-500 fill-current" />
                            )}
                            <span className="text-sm text-black/60 dark:text-white/60 whitespace-nowrap">
                              {formatTimestamp(message.timestamp)}
                            </span>
                          </div>
                        </div>
                        
                        <p className="text-sm text-black/60 dark:text-white/60 mb-2">
                          From: {message.from}
                        </p>
                        
                        <p className={cn(
                          "text-sm line-clamp-2",
                          message.isRead 
                            ? "text-black/60 dark:text-white/60" 
                            : "text-black/80 dark:text-white/80"
                        )}>
                          {message.preview}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
                
                {filteredMessages.length === 0 && (
                  <div className="text-center py-12">
                    <Mail className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-semibold text-black dark:text-white mb-2">
                      No messages found
                    </h3>
                    <p className="text-black/60 dark:text-white/60">
                      {filter === 'unread' && 'No unread messages'}
                      {filter === 'starred' && 'No starred messages'}
                      {filter === 'all' && 'Your inbox is empty'}
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentInboxPage;
