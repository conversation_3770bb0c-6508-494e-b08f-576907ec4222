// pages/student/[slug]/inbox.tsx
import { useParams } from 'blade/hooks';
import { use } from 'blade/server/hooks';
import StudentInboxPage from '../../../components/student/StudentInboxPage.client';

const StudentInboxPageRoute = () => {
  const { slug } = useParams();

  // Server-side data fetching - get current student
  const student = use.users({
    with: { slug: slug, role: 'student' }
  });

  if (!student) {
    return (
      <div className="p-6">
        <h1 className="text-2xl font-bold text-red-600">Student not found</h1>
      </div>
    );
  }

  return <StudentInboxPage student={student} />;
};

export default StudentInboxPageRoute;
